import { But<PERSON>, Drawer, Hint, Table, toast } from "@medusajs/ui"
import { useTranslation } from "react-i18next"
import { useAddCompanyToCustomerGroup, useRemoveCompanyFromCustomerGroup } from "../../../../../hooks/api/companies"
import { Company } from "../../../../../types"
import { HttpTypes } from "@medusajs/types"

interface CompanyCustomerGroupDrawerProps {
  company: Company
  customerGroups?: HttpTypes.AdminCustomerGroup[]
  open: boolean
  setOpen: (open: boolean) => void
}

export const CompanyCustomerGroupDrawer = ({ 
  company, 
  customerGroups, 
  open, 
  setOpen 
}: CompanyCustomerGroupDrawerProps) => {
  const { t } = useTranslation()
  
  const { mutateAsync: addMutate, isPending: addLoading } = useAddCompanyToCustomerGroup(company.id, {
    onSuccess: () => {
      toast.success("成功添加到客户组")
      setOpen(false)
    },
    onError: () => {
      toast.error("添加到客户组失败")
    },
  })

  const { mutateAsync: removeMutate, isPending: removeLoading } = useRemoveCompanyFromCustomerGroup(company.id, {
    onSuccess: () => {
      toast.success("成功从客户组移除")
    },
    onError: () => {
      toast.error("从客户组移除失败")
    },
  })

  const handleAdd = async (groupId: string) => {
    await addMutate(groupId)
  }

  const handleRemove = async (groupId: string) => {
    await removeMutate(groupId)
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <Drawer.Content className="z-50">
        <Drawer.Header>
          <Drawer.Title>管理客户组</Drawer.Title>
        </Drawer.Header>
        
        <Drawer.Body className="space-y-4 h-full overflow-y-hidden">
          <Hint variant="info">
            为公司 "{company.name}" 管理客户组。该公司有 {company.employees?.length || 0} 名员工。
          </Hint>

          <div className="overflow-y-auto">
            <Table>
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>客户组名称</Table.HeaderCell>
                  <Table.HeaderCell className="text-right">操作</Table.HeaderCell>
                </Table.Row>
              </Table.Header>

              <Table.Body>
                {customerGroups ? (
                  customerGroups.map((group) => (
                    <Table.Row key={group.id}>
                      <Table.Cell>{group.name}</Table.Cell>
                      <Table.Cell className="text-right">
                        {company.customer_group?.id &&
                        company.customer_group.id === group.id ? (
                          <Button
                            onClick={() => handleRemove(group.id)}
                            isLoading={removeLoading}
                            variant="danger"
                            size="small"
                          >
                            移除
                          </Button>
                        ) : (
                          <Button
                            onClick={() => handleAdd(group.id)}
                            disabled={
                              (company.customer_group?.id &&
                                company.customer_group.id !== group.id) ||
                              addLoading
                            }
                            isLoading={addLoading}
                            size="small"
                          >
                            添加
                          </Button>
                        )}
                      </Table.Cell>
                    </Table.Row>
                  ))
                ) : (
                  <Table.Row>
                    <Table.Cell colSpan={2} className="text-center">
                      暂无客户组
                    </Table.Cell>
                  </Table.Row>
                )}
              </Table.Body>
            </Table>
          </div>
        </Drawer.Body>
      </Drawer.Content>
    </Drawer>
  )
}

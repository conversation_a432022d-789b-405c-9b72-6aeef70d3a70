{"$schema": "./$schema.json", "general": {"ascending": "升序", "descending": "降序", "add": "添加", "start": "开始", "end": "结束", "open": "打开", "close": "关闭", "apply": "应用", "range": "范围", "search": "搜索", "of": "/", "results": "条记录", "pages": "页", "next": "下一页", "prev": "上一页", "is": "是", "timeline": "时间线", "success": "成功", "warning": "警告", "tip": "提示", "error": "错误", "select": "选择", "selected": "已选择", "enabled": "已启用", "disabled": "已禁用", "expired": "已过期", "active": "活跃", "revoked": "已撤销", "new": "新建", "modified": "已修改", "added": "已添加", "removed": "已移除", "admin": "管理员", "store": "商店", "details": "详情", "items_one": "{{count}} 项", "items_other": "{{count}} 项", "countSelected": "已选择 {{count}} 项", "countOfTotalSelected": "已选择 {{count}}/{{total}} 项", "plusCount": "+ {{count}}", "plusCountMore": "还有 {{count}} 项", "areYouSure": "确定吗?", "areYouSureDescription": "您即将删除 {{entity}} {{title}}。此操作无法撤消。", "noRecordsFound": "未找到记录", "typeToConfirm": "请输入 {val} 以确认:", "noResultsTitle": "无结果", "noResultsMessage": "请尝试更改筛选条件或搜索关键词", "noSearchResults": "无搜索结果", "noSearchResultsFor": "未找到与 <0>'{{query}}'</0> 相关的结果", "noRecordsTitle": "无记录", "noRecordsMessage": "暂无记录可显示", "unsavedChangesTitle": "确定要离开此表单吗?", "unsavedChangesDescription": "您有未保存的更改,离开后这些更改将丢失。", "includesTaxTooltip": "此列中的价格包含税费。", "excludesTaxTooltip": "此列中的价格不含税费。", "noMoreData": "没有更多数据", "actions": "操作"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}} 个键", "numberOfKeys_other": "{{count}} 个键", "drawer": {"header_one": "JSON <0>· {{count}} 个键</0>", "header_other": "JSON <0>· {{count}} 个键</0>", "description": "查看此对象的 JSON 数据。"}}, "metadata": {"header": "元数据", "numberOfKeys_one": "{{count}} 个键", "numberOfKeys_other": "{{count}} 个键", "edit": {"header": "编辑元数据", "description": "编辑此对象的元数据。", "successToast": "元数据已成功更新。", "actions": {"insertRowAbove": "在上方插入行", "insertRowBelow": "在下方插入行", "deleteRow": "删除行"}, "labels": {"key": "键", "value": "值"}, "complexRow": {"label": "部分行已禁用", "description": "此对象包含无法在此处编辑的非基本元数据,如数组或对象。要编辑已禁用的行,请直接使用 API。", "tooltip": "此行已禁用,因为它包含非基本数据。"}}}, "validation": {"mustBeInt": "值必须是整数。", "mustBePositive": "值必须是正数。"}, "actions": {"save": "保存", "saveAsDraft": "保存为草稿", "copy": "复制", "copied": "已复制", "duplicate": "复制", "publish": "发布", "create": "创建", "delete": "删除", "remove": "移除", "revoke": "撤销", "cancel": "取消", "forceConfirm": "强制确认", "continueEdit": "继续编辑", "enable": "启用", "disable": "禁用", "undo": "撤销", "complete": "完成", "viewDetails": "查看详情", "back": "返回", "close": "关闭", "showMore": "显示更多", "continue": "继续", "continueWithEmail": "使用邮箱继续", "idCopiedToClipboard": "ID已复制到剪贴板", "addReason": "添加原因", "addNote": "添加备注", "reset": "重置", "confirm": "确认", "edit": "编辑", "addItems": "添加项目", "download": "下载", "clear": "清除", "clearAll": "清除全部", "apply": "应用", "add": "添加", "select": "选择", "browse": "浏览", "logout": "退出登录", "hide": "隐藏", "export": "导出", "import": "导入", "cannotUndo": "此操作无法撤销"}, "operators": {"in": "包含"}, "app": {"search": {"label": "搜索", "title": "搜索", "description": "搜索整个商店,包括订单、产品、客户等。", "allAreas": "所有区域", "navigation": "导航", "openResult": "打开结果", "showMore": "显示更多", "placeholder": "跳转或搜索任何内容...", "noResultsTitle": "未找到结果", "noResultsMessage": "我们找不到与您的搜索相匹配的内容。", "emptySearchTitle": "输入关键词搜索", "emptySearchMessage": "输入关键词或短语以开始探索。", "loadMore": "加载更多 {{count}} 项", "groups": {"all": "所有区域", "customer": "客户", "customerGroup": "客户组", "product": "产品", "productVariant": "产品变体", "inventory": "库存", "reservation": "预订", "category": "分类", "collection": "系列", "order": "订单", "promotion": "促销", "campaign": "活动", "priceList": "价格表", "user": "用户", "region": "地区", "taxRegion": "税收地区", "returnReason": "退货原因", "salesChannel": "销售渠道", "productType": "产品类型", "productTag": "产品标签", "location": "位置", "shippingProfile": "配送方案", "publishableApiKey": "可发布的API密钥", "secretApiKey": "密钥API密钥", "command": "命令", "navigation": "导航"}}, "keyboardShortcuts": {"pageShortcut": "跳转到", "settingShortcut": "设置", "commandShortcut": "命令", "then": "然后", "navigation": {"goToOrders": "订单", "goToProducts": "产品", "goToCollections": "系列", "goToCategories": "分类", "goToCustomers": "客户", "goToCustomerGroups": "客户组", "goToInventory": "库存", "goToReservations": "预订", "goToPriceLists": "价格表", "goToPromotions": "促销", "goToCampaigns": "活动"}, "settings": {"goToSettings": "设置", "goToStore": "商店", "goToUsers": "用户", "goToRegions": "地区", "goToTaxRegions": "税收地区", "goToSalesChannels": "销售渠道", "goToProductTypes": "产品类型", "goToLocations": "位置", "goToPublishableApiKeys": "可发布的API密钥", "goToSecretApiKeys": "密钥API密钥", "goToWorkflows": "工作流", "goToProfile": "个人资料", "goToReturnReasons": "退货原因"}}, "menus": {"user": {"documentation": "文档", "changelog": "更新日志", "shortcuts": "快捷键", "profileSettings": "个人资料设置", "theme": {"label": "主题", "dark": "深色", "light": "浅色", "system": "跟随系统"}}, "store": {"label": "商店", "storeSettings": "商店设置"}, "actions": {"logout": "退出登录"}}, "nav": {"accessibility": {"title": "导航", "description": "仪表板的导航菜单。"}, "common": {"extensions": "扩展"}, "main": {"store": "商店", "storeSettings": "商店设置"}, "settings": {"header": "设置", "general": "通用", "developer": "开发者", "myAccount": "我的账户"}}}, "dataGrid": {"columns": {"view": "查看", "resetToDefault": "重置为默认", "disabled": "已禁用更改可见列。"}, "shortcuts": {"label": "快捷键", "commands": {"undo": "撤销", "redo": "重做", "copy": "复制", "paste": "粘贴", "edit": "编辑", "delete": "删除", "clear": "清除", "moveUp": "向上移动", "moveDown": "向下移动", "moveLeft": "向左移动", "moveRight": "向右移动", "moveTop": "移到顶部", "moveBottom": "移到底部", "selectDown": "向下选择", "selectUp": "向上选择", "selectColumnDown": "向下选择列", "selectColumnUp": "向上选择列", "focusToolbar": "聚焦工具栏", "focusCancel": "聚焦取消"}}, "errors": {"fixError": "修复错误", "count_one": "{{count}} 个错误", "count_other": "{{count}} 个错误"}}, "filters": {"sortLabel": "排序", "filterLabel": "过滤", "searchLabel": "搜索", "date": {"today": "今天", "lastSevenDays": "最近7天", "lastThirtyDays": "最近30天", "lastNinetyDays": "最近90天", "lastTwelveMonths": "最近12个月", "custom": "自定义", "from": "从", "to": "至", "starting": "开始于", "ending": "结束于"}, "compare": {"lessThan": "小于", "greaterThan": "大于", "exact": "精确", "range": "范围", "lessThanLabel": "小于 {{value}}", "greaterThanLabel": "大于 {{value}}", "andLabel": "和"}, "sorting": {"alphabeticallyAsc": "A to Z", "alphabeticallyDesc": "Z to A", "dateAsc": "按最新的", "dateDesc": "按最旧的"}, "radio": {"yes": "Yes", "no": "No", "true": "True", "false": "False"}, "addFilter": "添加筛选"}, "errorBoundary": {"badRequestTitle": "400 - 错误请求", "badRequestMessage": "由于语法错误,服务器无法理解该请求。", "notFoundTitle": "404 - 此地址不存在", "notFoundMessage": "请检查URL并重试,或使用搜索栏查找您要找的内容。", "internalServerErrorTitle": "500 - 服务器内部错误", "internalServerErrorMessage": "服务器发生意外错误。请稍后重试。", "defaultTitle": "发生错误", "defaultMessage": "渲染此页面时发生意外错误。", "noMatchMessage": "您要查找的页面不存在。", "backToDashboard": "返回仪表板"}, "addresses": {"title": "地址", "shippingAddress": {"header": "配送地址", "editHeader": "编辑配送地址", "editLabel": "配送地址", "label": "配送地址"}, "billingAddress": {"header": "账单地址", "editHeader": "编辑账单地址", "editLabel": "账单地址", "label": "账单地址", "sameAsShipping": "与配送地址相同"}, "contactHeading": "联系方式", "locationHeading": "位置"}, "email": {"editHeader": "编辑邮箱", "editLabel": "邮箱", "label": "邮箱"}, "transferOwnership": {"header": "转移所有权", "label": "转移所有权", "details": {"order": "订单详情", "draft": "草稿详情"}, "currentOwner": {"label": "当前所有者", "hint": "订单的当前所有者。"}, "newOwner": {"label": "新所有者", "hint": "要将订单转移给的新所有者。"}, "validation": {"mustBeDifferent": "新所有者必须与当前所有者不同。", "required": "必须指定新所有者。"}}, "sales_channels": {"availableIn": "在 <1>{{y}}</1> 个销售渠道中的 <0>{{x}}</0> 个可用"}, "products": {"domain": "产品", "list": {"noRecordsMessage": "创建您的第一个产品以开始销售。"}, "edit": {"header": "编辑产品", "description": "编辑产品详情。", "successToast": "产品 {{title}} 更新成功。"}, "create": {"title": "创建产品", "description": "创建新产品。", "header": "常规", "tabs": {"details": "详情", "organize": "组织", "variants": "变体", "inventory": "库存套件"}, "errors": {"variants": "请至少选择一个变体。", "options": "请至少创建一个选项。", "uniqueSku": "SKU 必须唯一。"}, "inventory": {"heading": "库存套件", "label": "将库存项目添加到变体的库存套件中。", "itemPlaceholder": "选择库存项目", "quantityPlaceholder": "套件需要多少这种商品？"}, "variants": {"header": "变体", "subHeadingTitle": "是的,这是一个带有变体的产品", "subHeadingDescription": "取消选中时,我们将为您创建一个默认变体", "optionTitle": {"placeholder": "尺寸"}, "optionValues": {"placeholder": "小号、中号、大号"}, "productVariants": {"label": "产品变体", "hint": "此排序将影响变体在您店面中的显示顺序。", "alert": "添加选项以创建变体。", "tip": "未选中的变体将不会被创建。您可以随时创建和编辑变体,但此列表符合您的产品选项变化。"}, "productOptions": {"label": "产品选项", "hint": "定义产品的选项,例如颜色、尺寸等。"}}, "successToast": "产品 {{title}} 创建成功。"}, "export": {"header": "导出产品列表", "description": "将产品列表导出为 CSV 文件。", "success": {"title": "我们正在处理您的导出", "description": "导出数据可能需要几分钟。完成后我们会通知您。"}, "filters": {"title": "筛选", "description": "在表格概览中应用筛选整此视图"}, "columns": {"title": "列", "description": "自定义导出数据以满足特定需求"}}, "import": {"header": "导入产品列表", "uploadLabel": "导入产品", "uploadHint": "拖放 CSV 文件或点击上传", "description": "通过提供预定义格式的 CSV 文件导入产品", "template": {"title": "不确定如何整理您的列表？", "description": "下载下面的模板以确保您遵循正确的格式。"}, "upload": {"title": "上传 CSV 文件", "description": "通过导入,您可以添加或更新产品。要更新现有产品,您必须使用现有的标识符和 ID,要更新现有变体,您必须使用现有的 ID。导入产品前我们会要求您确认。", "preprocessing": "预处理中...", "productsToCreate": "将创建产品", "productsToUpdate": "将更新产品"}, "success": {"title": "我们正在处理您的导入", "description": "导入数据可能需要一段时间。完成后我们会通知您。"}}, "deleteWarning": "您即将删除产品 {{title}}。此操作无法撤销。", "variants": {"header": "变体", "empty": {"heading": "没有变体", "description": "这里没有变体可显示。"}, "filtered": {"heading": "无结果", "description": "没有变体符合当前过滤条件。"}}, "attributes": "属性", "editAttributes": "编辑属性", "editOptions": "编辑选项", "editPrices": "编辑价格", "media": {"label": "媒体", "editHint": "添加媒体到产品以在您的店面展示它。", "makeThumbnail": "设为缩略图", "uploadImagesLabel": "上传图片", "uploadImagesHint": "将图片拖放到此处或点击上传。", "invalidFileType": "'{{name}}' 不是支持的文件类型。支持的文件类型有：{{types}}。", "failedToUpload": "添加的媒体上传失败。请重试。", "deleteWarning_one": "您即将删除 {{count}} 张图片。此操作无法撤销。", "deleteWarning_other": "您即将删除 {{count}} 张图片。此操作无法撤销。", "deleteWarningWithThumbnail_one": "您即将删除 {{count}} 张图片(包括缩略图)。此操作无法撤销。", "deleteWarningWithThumbnail_other": "您即将删除 {{count}} 张图片(包括缩略图)。此操作无法撤销。", "thumbnailTooltip": "缩略图", "galleryLabel": "图库", "downloadImageLabel": "下载当前图片", "deleteImageLabel": "删除当前图片", "emptyState": {"header": "暂无媒体", "description": "添加媒体到产品以在您的店面展示它。", "action": "添加媒体"}, "successToast": "媒体更新成功。"}, "discountableHint": "取消选中时,折扣将不会应用于此产品。", "noSalesChannels": "在任何销售渠道中都不可用", "variantCount_one": "{{count}} 个变体", "variantCount_other": "{{count}} 个变体", "deleteVariantWarning": "您即将删除变体 {{title}}。此操作无法撤销。", "productStatus": {"draft": "草稿", "published": "已发布", "proposed": "已提议", "rejected": "已拒绝"}, "fields": {"title": {"label": "标题", "hint": "为您的产品提供一个简短明确的标题。<0/>搜索引擎推荐的长度是 50-60 个字符。", "placeholder": "冬季夹克"}, "subtitle": {"label": "副标题", "placeholder": "温暖舒适"}, "handle": {"label": "标识", "tooltip": "标识用于在您的店面引用产品。如果未指定,将根据产品标题生成标识。", "placeholder": "冬衣"}, "description": {"label": "描述", "hint": "为您的产品提供一个简短明确的描述。<0/>搜索引擎推荐的长度是 120-160 个字符。", "placeholder": "一件温暖舒适的外套"}, "discountable": {"label": "可打折", "hint": "取消选中时,折扣将不会应用于此产品"}, "shipping_profile": {"label": "运输概况", "hint": "将产品连接到运输配置文件"}, "type": {"label": "类型"}, "collection": {"label": "系列"}, "categories": {"label": "分类"}, "tags": {"label": "标签"}, "sales_channels": {"label": "销售渠道", "hint": "如果不设置,此产品将仅在默认销售渠道中可用。"}, "countryOrigin": {"label": "原产国"}, "material": {"label": "材料"}, "width": {"label": "宽度"}, "length": {"label": "长度"}, "height": {"label": "高度"}, "weight": {"label": "重量"}, "options": {"label": "产品选项", "hint": "选项用于定义产品的颜色、尺寸等", "add": "添加选项", "optionTitle": "选项标题", "optionTitlePlaceholder": "颜色", "variations": "变体(用逗号分隔)", "variantionsPlaceholder": "红色、蓝色、绿色"}, "variants": {"label": "产品变体", "hint": "未选中的变体将不会被创建,此排序将影响变体在前端的排序方式。"}, "mid_code": {"label": "MID 代码"}, "hs_code": {"label": "HS 代码"}}, "variant": {"edit": {"header": "编辑变体", "success": "产品变体编辑成功"}, "create": {"header": "变体详情"}, "deleteWarning": "您确定要删除此变体吗？", "pricesPagination": "第 1-{{current}} 个价格,共 {{total}} 个", "tableItemAvailable": "{{availableCount}} 个可用", "tableItem_one": "{{availableCount}} 个可用,位于 {{locationCount}} 个位置", "tableItem_other": "{{availableCount}} 个可用,位于 {{locationCount}} 个位置", "inventory": {"notManaged": "未管理", "manageItems": "管理库存项目", "notManagedDesc": "此变体未管理库存。启用`管理库存`以跟踪变体的库存。", "manageKit": "管理库存套件", "navigateToItem": "转到库存项目", "actions": {"inventoryItems": "转到库存项目", "inventoryKit": "显示库存项目"}, "inventoryKit": "库存套件", "inventoryKitHint": "此变体是否由多个库存项目组成？", "validation": {"itemId": "请选择库存项目。", "quantity": "数量为必填项。请输入一个正数。"}, "header": "库存与库存", "editItemDetails": "编辑项目详情", "manageInventoryLabel": "管理库存", "manageInventoryHint": "启用后,我们将在创建订单和退货时为您更改库存数量。", "allowBackordersLabel": "允许缺货订购", "allowBackordersHint": "启用后,即使没有可用数量,客户也可以购买该变体。", "toast": {"levelsBatch": "库存水平已更新。", "update": "库存项目更新成功。", "updateLevel": "库存水平更新成功。", "itemsManageSuccess": "库存项目更新成功。"}}}, "options": {"header": "选项", "edit": {"header": "编辑选项", "successToast": "选项 {{title}} 更新成功。"}, "create": {"header": "创建选项", "successToast": "选项 {{title}} 创建成功。"}, "deleteWarning": "您即将删除产品选项：{{title}}。此操作无法撤销。"}, "organization": {"header": "组织", "edit": {"header": "编辑组织", "toasts": {"success": "{{title}} 的组织更新成功。"}}}, "stock": {"heading": "管理产品库存数量和库存地点", "description": "更新所有产品变体的库存数量。", "loading": "请稍候,这可能需要一点时间...", "tooltips": {"alreadyManaged": "此库存项目已可在 {{title}} 下编辑。", "alreadyManagedWithSku": "此库存项目已可在 {{title}} ({{sku}}) 下编辑。"}}, "shippingProfile": {"header": "运输配置", "edit": {"header": "运输配置", "toasts": {"success": "已成功更新 {{title}} 的运输资料。"}}, "create": {"errors": {"required": "需要运输资料"}}}, "toasts": {"delete": {"success": {"header": "产品已删除", "description": "{{title}} 删除成功。"}, "error": {"header": "删除产品失败"}}}}, "collections": {"domain": "系列", "subtitle": "将产品组织到系列中。", "createCollection": "创建系列", "createCollectionHint": "创建新系列以组织您的产品。", "createSuccess": "系列创建成功。", "editCollection": "编辑系列", "handleTooltip": "标识用于在您的店面引用系列。如果未指定,将根据系列标题生成标识。", "deleteWarning": "您即将删除系列 {{title}}。此操作无法撤销。", "removeSingleProductWarning": "您即将从系列中移除产品 {{title}}。此操作无法撤销。", "removeProductsWarning_one": "您即将从系列中移除 {{count}} 个产品。此操作无法撤销。", "removeProductsWarning_other": "您即将从系列中移除 {{count}} 个产品。此操作无法撤销。", "products": {"list": {"noRecordsMessage": "系列中没有产品。"}, "add": {"successToast_one": "产品已成功添加到系列。", "successToast_other": "产品已成功添加到系列。"}, "remove": {"successToast_one": "产品已成功从系列中移除。", "successToast_other": "产品已成功从系列中移除。"}}}, "categories": {"domain": "分类", "subtitle": "将产品组织到分类中,并管理这些分类的排序和层级。", "create": {"header": "创建分类", "hint": "创建新分类以组织您的产品。", "tabs": {"details": "详情", "organize": "组织排序"}, "successToast": "分类 {{name}} 创建成功。"}, "edit": {"header": "编辑分类", "description": "编辑分类以更新其详情。", "successToast": "分类更新成功。"}, "delete": {"confirmation": "您即将删除分类 {{name}}。此操作无法撤销。", "successToast": "分类 {{name}} 删除成功。"}, "products": {"add": {"disabledTooltip": "该产品此分类中。", "successToast_one": "已将 {{count}} 个产品添加到分类。", "successToast_other": "已将 {{count}} 个产品添加到分类。"}, "remove": {"confirmation_one": "您即将从分类中移除 {{count}} 个产品。此操作无法撤销。", "confirmation_other": "您即将从分类中移除 {{count}} 个产品。此操作无法撤销。", "successToast_one": "已从分类中移除 {{count}} 个产品。", "successToast_other": "已从分类中移除 {{count}} 个产品。"}, "list": {"noRecordsMessage": "分类中没有产品。"}}, "organize": {"header": "组织", "action": "编辑排序"}, "fields": {"visibility": {"label": "可见性", "internal": "内部", "public": "公开"}, "status": {"label": "状态", "active": "活跃", "inactive": "不活跃"}, "path": {"label": "路径", "tooltip": "显示分类的完整路径。"}, "children": {"label": "子分类"}, "new": {"label": "新建"}}}, "inventory": {"domain": "库存", "subtitle": "管理您的库存项目", "reserved": "已预留", "available": "可用", "locationLevels": "位置", "associatedVariants": "关联变体", "manageLocations": "管理位置", "manageLocationQuantity": "管理位置数量", "deleteWarning": "您即将删除一个库存项目。此操作无法撤消。", "editItemDetails": "编辑项目详情", "quantityAcrossLocations": "{{quantity}} 分布于 {{locations}} 个地点", "create": {"title": "创建库存项目", "details": "详情", "availability": "可用性", "locations": "位置", "attributes": "属性", "requiresShipping": "需要配送", "requiresShippingHint": "该库存项目是否需要配送？", "successToast": "库存项目创建成功。"}, "reservation": {"header": "预留 {{itemName}}", "editItemDetails": "编辑预留", "lineItemId": "行项目ID", "orderID": "订单ID", "description": "描述", "location": "位置", "inStockAtLocation": "该位置库存", "availableAtLocation": "该位置可用", "reservedAtLocation": "该位置已预留", "reservedAmount": "预留数量", "create": "创建预留", "itemToReserve": "要预留的项目", "quantityPlaceholder": "您想预留多少？", "descriptionPlaceholder": "这是什么类型的预留？", "successToast": "预留创建成功。", "updateSuccessToast": "预留更新成功。", "deleteSuccessToast": "预留删除成功。", "errors": {"noAvaliableQuantity": "库存位置没有可用数量。", "quantityOutOfRange": "最小数量为1，最大数量为{{max}}"}}, "adjustInventory": {"errors": {"stockedQuantity": "库存数量不能更新为小于已预留数量{{quantity}}。"}}, "toast": {"updateLocations": "位置更新成功。", "updateLevel": "库存水平更新成功。", "updateItem": "库存项目更新成功。"}, "stock": {"title": "更新库存数量", "description": "更新所选库存项目的库存数量。", "action": "编辑库存数量", "placeholder": "未启用", "disablePrompt_one": "您即将禁用 {{count}} 个库存地点。此操作无法撤消。", "disablePrompt_other": "您即将禁用 {{count}} 个库存地点。此操作无法撤消。", "disabledToggleTooltip": "无法禁用：禁用前请先清除在途和/或预留数量。", "successToast": "库存数量更新成功。"}}, "giftCards": {"domain": "礼品卡", "editGiftCard": "编辑礼品卡", "createGiftCard": "创建礼品卡", "createGiftCardHint": "手动创建可用作商店支付方式的礼品卡。", "selectRegionFirst": "请先选择地区", "deleteGiftCardWarning": "您即将删除礼品卡 {{code}}。此操作无法撤消。", "balanceHigherThanValue": "余额不能高于原始金额。", "balanceLowerThanZero": "余额不能为负数。", "expiryDateHint": "不同国家对礼品卡有效期有不同的法律规定。设置有效期前请确保查看当地法规。", "regionHint": "更改礼品卡的地区也会更改其货币，可能会影响其货币价值。", "enabledHint": "指定礼品卡是启用还是禁用。", "balance": "余额", "currentBalance": "当前余额", "initialBalance": "初始余额", "personalMessage": "个人留言", "recipient": "接收人"}, "customers": {"domain": "客户", "list": {"noRecordsMessage": "您的客户将显示在这里。"}, "create": {"header": "创建客户", "hint": "创建新客户并管理其详细信息。", "successToast": "客户 {{email}} 创建成功。"}, "groups": {"label": "客户群组", "remove": "您确定要将客户从 {{name }}客户群组中移除吗？", "removeMany": "您确定要将客户从以下客户群组中移除：{{groups}}？", "alreadyAddedTooltip": "该客户已在此客户群组中。", "list": {"noRecordsMessage": "该客户不属于任何群组。"}, "add": {"success": "客户已添加到：{{groups}}。", "list": {"noRecordsMessage": "请先创建客户群组。"}}, "removed": {"success": "客户已从以下群组中移除：{{groups}}。", "list": {"noRecordsMessage": "请先创建客户群组。"}}}, "edit": {"header": "编辑客户", "emailDisabledTooltip": "已注册客户的电子邮件地址无法更改。", "successToast": "客户 {{email}} 更新成功。"}, "actions": {"editDetails": "编辑客户详情"}, "delete": {"title": "删除客户", "description": "您即将删除客户 {{email}}。此操作无法撤消。", "successToast": "客户 {{email}} 删除成功。"}, "fields": {"guest": "访客", "registered": "已注册", "groups": "群组"}, "registered": "已注册", "guest": "访客", "hasAccount": "拥有账户", "addresses": {"title": "地址", "fields": {"addressName": "地址名称", "address1": "地址1", "address2": "地址2", "city": "城市", "province": "省份", "postalCode": "邮政编码", "country": "国家", "phone": "电话", "company": "公司", "countryCode": "国家代码", "provinceCode": "省份代码"}, "create": {"header": "创建地址", "hint": "为客户创建新地址。", "successToast": "地址创建成功。"}}}, "customerGroups": {"domain": "客户群组", "subtitle": "将客户组织成群组。群组可以有不同的促销和价格。", "list": {"empty": {"heading": "暂无客户组", "description": "当前没有可显示的客户组。"}, "filtered": {"heading": "无搜索结果", "description": "没有符合当前筛选条件的客户组。"}}, "create": {"header": "创建客户群组", "hint": "创建新的客户群组以细分您的客户。", "successToast": "客户群组 {{name}} 创建成功。"}, "edit": {"header": "编辑客户群组", "successToast": "客户群组 {{name}} 更新成功。"}, "delete": {"title": "删除客户群组", "description": "您即将删除客户群组 {{name}}。此操作无法撤消。", "successToast": "客户群组 {{name}} 删除成功。"}, "customers": {"alreadyAddedTooltip": "该客户已添加到群组。", "add": {"successToast_one": "客户已成功添加到群组。", "successToast_other": "客户已成功添加到群组。", "list": {"noRecordsMessage": "请先创建客户。"}}, "remove": {"title_one": "移除客户", "title_other": "移除客户", "description_one": "您即将从客户群组中移除 {{count}} 个客户。此操作无法撤消。", "description_other": "您即将从客户群组中移除 {{count}} 个客户。此操作无法撤消。"}, "list": {"noRecordsMessage": "该群组没有客户。"}}}, "orders": {"domain": "订单", "claim": "索赔", "exchange": "换货", "return": "退货", "cancelWarning": "您即将取消订单 {{id}}。此操作无法撤消。", "orderCanceled": "订单取消成功！", "onDateFromSalesChannel": "{{date}} 来自 {{salesChannel}}", "list": {"noRecordsMessage": "您的订单将显示在这里。"}, "status": {"not_paid": "未支付", "pending": "处理中", "completed": "已完成", "draft": "草稿", "archived": "已归档", "canceled": "已取消", "requires_action": "需要处理"}, "summary": {"requestReturn": "申请退货", "allocateItems": "分配商品", "editOrder": "编辑订单", "editOrderContinue": "继续编辑订单", "inventoryKit": "包含 {{count}}x 库存项目", "itemTotal": "商品总计", "shippingTotal": "运费总计", "discountTotal": "折扣总计", "taxTotalIncl": "税费总计（已含）", "itemSubtotal": "商品小计", "shippingSubtotal": "运费小计", "discountSubtotal": "折扣小计", "taxTotal": "税费总计"}, "transfer": {"title": "转移所有权", "requestSuccess": "订单转移请求已发送至：{{email}}。", "currentOwner": "当前所有者", "newOwner": "新所有者", "currentOwnerDescription": "当前与此订单关联的客户。", "newOwnerDescription": "要将此订单转移给的客户。"}, "payment": {"title": "支付", "isReadyToBeCaptured": "支付 <0/> 已准备好被收取。", "totalPaidByCustomer": "客户已支付总额", "capture": "收取支付", "capture_short": "收取", "refund": "退款", "markAsPaid": "标记为已支付", "statusLabel": "支付状态", "statusTitle": "支付状态", "status": {"notPaid": "未支付", "authorized": "已授权", "partiallyAuthorized": "部分已授权", "awaiting": "等待中", "captured": "已收取", "partiallyRefunded": "部分已退款", "partiallyCaptured": "部分已收取", "refunded": "已退款", "canceled": "已取消", "requiresAction": "需要操作"}, "capturePayment": "将收取 {{amount}} 的支付。", "capturePaymentSuccess": "成功收取 {{amount}} 的支付", "markAsPaidPayment": "{{amount}} 的支付将被标记为已支付。", "markAsPaidPaymentSuccess": "{{amount}} 的支付已成功标记为已支付", "createRefund": "创建退款", "refundPaymentSuccess": "退款金额 {{amount}} 成功", "createRefundWrongQuantity": "数量应该在 1 和 {{number}} 之间", "refundAmount": "退款 {{ amount }}", "paymentLink": "复制 {{ amount }} 的支付链接", "selectPaymentToRefund": "选择要退款的支付"}, "edits": {"title": "编辑订单", "confirm": "确认编辑", "confirmText": "您即将确认订单编辑。此操作无法撤消。", "cancel": "取消编辑", "currentItems": "当前商品", "currentItemsDescription": "调整商品数量或移除。", "addItemsDescription": "您可以向订单添加新商品。", "addItems": "添加商品", "amountPaid": "已支付金额", "currentTotal": "原始总价", "newTotal": "新总价", "differenceDue": "应付差额", "create": "编辑订单", "noteHint": "为编辑添加内部备注", "cancelSuccessToast": "订单编辑已取消", "createSuccessToast": "订单编辑请求已创建", "activeChangeError": "订单上已有活动的订单变更（退货、索赔、换货等）。请在编辑订单前完成或取消变更。", "panel": {"title": "已请求订单编辑", "titlePending": "订单编辑待处理"}, "toast": {"canceledSuccessfully": "订单编辑已取消", "confirmedSuccessfully": "订单编辑已确认"}, "validation": {"quantityLowerThanFulfillment": "不能将数量设置为小于或等于已履行数量"}}, "edit": {"email": {"title": "编辑邮箱", "requestSuccess": "订单邮箱已更新为 {{email}}。"}, "shippingAddress": {"title": "编辑配送地址", "requestSuccess": "订单配送地址已更新。"}, "billingAddress": {"title": "编辑账单地址", "requestSuccess": "订单账单地址已更新。"}}, "returns": {"create": "创建退货", "confirm": "确认退货", "confirmText": "您即将确认退货。此操作无法撤消。", "inbound": "入库", "outbound": "出库", "sendNotification": "发送通知", "sendNotificationHint": "通知客户关于退货。", "returnTotal": "退货总计", "inboundTotal": "入库总计", "estDifference": "估计差异", "outstandingAmount": "未结金额", "reason": "原因", "reasonHint": "选择客户想要退货的原因。", "note": "备注", "noInventoryLevel": "无库存水平", "noInventoryLevelDesc": "所选位置没有所选商品的库存水平。可以请求退货，但在为所选位置创建库存水平之前无法接收。", "noteHint": "如果您想指定某些内容，可以自由输入。", "location": "位置", "locationHint": "选择您想要将商品退回的位置。", "inboundShipping": "退货配送", "inboundShippingHint": "选择您想使用的方式。", "returnableQuantityLabel": "可退货数量", "refundableAmountLabel": "可退款金额", "returnRequestedInfo": "已请求退货 {{requestedItemsCount}}x 件商品", "returnReceivedInfo": "已收到退货 {{requestedItemsCount}}x 件商品", "itemReceived": "已收到商品", "returnRequested": "已请求退货", "damagedItemReceived": "已收到损坏商品", "damagedItemsReturned": "已退回 {{quantity}}x 件损坏商品", "activeChangeError": "此订单正在进行活动订单变更。请先完成或放弃变更。", "cancel": {"title": "取消退货", "description": "您确定要取消退货请求吗？"}, "placeholders": {"noReturnShippingOptions": {"title": "未找到退货配送选项", "hint": "该位置未创建退货配送选项。您可以在<LinkComponent>位置与配送</LinkComponent>中创建。"}, "outboundShippingOptions": {"title": "未找到出库配送选项", "hint": "该位置未创建出库配送选项。您可以在<LinkComponent>位置与配送</LinkComponent>中创建。"}}, "receive": {"action": "接收商品", "receiveItems": "{{ returnType }} {{ id }}", "restockAll": "重新入库所有商品", "itemsLabel": "已收到商品", "title": "接收 #{{returnId}} 的商品", "sendNotificationHint": "通知客户关于已收到的退货。", "inventoryWarning": "请注意，我们将根据您上面的输入自动调整库存水平。", "writeOffInputLabel": "有多少商品损坏？", "toast": {"success": "退货接收成功。", "errorLargeValue": "数量大于请求的商品数量。", "errorNegativeValue": "数量不能为负值。", "errorLargeDamagedValue": "损坏商品数量 + 未损坏接收数量超过退货上的总商品数量。请减少未损坏商品的数量。"}}, "toast": {"canceledSuccessfully": "退货已成功取消", "confirmedSuccessfully": "退货已成功确认"}, "panel": {"title": "已发起退货", "description": "有一个待完成的退货请求"}}, "claims": {"create": "创建索赔", "confirm": "确认索赔", "confirmText": "您即将确认索赔。此操作无法撤消。", "manage": "管理索赔", "outbound": "出库", "outboundItemAdded": "通过索赔添加了 {{itemsCount}}x", "outboundTotal": "出库总计", "outboundShipping": "出库配送", "outboundShippingHint": "选择您想使用的方式。", "refundAmount": "预计差额", "activeChangeError": "此订单有活动的订单变更。请先完成或放弃之前的变更。", "actions": {"cancelClaim": {"successToast": "索赔已成功取消。"}}, "cancel": {"title": "取消索赔", "description": "您确定要取消索赔吗？"}, "tooltips": {"onlyReturnShippingOptions": "此列表将仅包含退货配送选项。"}, "toast": {"canceledSuccessfully": "索赔已成功取消", "confirmedSuccessfully": "索赔已成功确认"}, "panel": {"title": "已发起索赔", "description": "有一个待完成的索赔请求"}}, "exchanges": {"create": "创建换货", "manage": "管理换货", "confirm": "确认换货", "confirmText": "您即将确认换货。此操作无法撤消。", "outbound": "出库", "outboundItemAdded": "通过换货添加了 {{itemsCount}}x", "outboundTotal": "出库总计", "outboundShipping": "出库配送", "outboundShippingHint": "选择您想使用的方式。", "refundAmount": "预计差额", "activeChangeError": "此订单有活动的订单变更。请先完成或放弃之前的变更。", "actions": {"cancelExchange": {"successToast": "换货已成功取消。"}}, "cancel": {"title": "取消换货", "description": "您确定要取消换货吗？"}, "tooltips": {"onlyReturnShippingOptions": "此列表将仅包含退货配送选项。"}, "toast": {"canceledSuccessfully": "换货已成功取消", "confirmedSuccessfully": "换货已成功确认"}, "panel": {"title": "已发起换货", "description": "有一个待完成的换货请求"}}, "reservations": {"allocatedLabel": "已分配", "notAllocatedLabel": "未分配"}, "allocateItems": {"action": "分配商品", "title": "分配订单商品", "locationDescription": "选择您想要从哪个位置分配。", "itemsToAllocate": "要分配的商品", "itemsToAllocateDesc": "选择您想要分配的商品数量", "search": "搜索商品", "consistsOf": "包含 {{num}}x 库存商品", "requires": "每个变体需要 {{num}}", "toast": {"created": "商品分配成功"}, "error": {"quantityNotAllocated": "有未分配的商品。"}}, "shipment": {"title": "标记履行已发货", "trackingNumber": "追踪号码", "addTracking": "添加追踪号码", "sendNotification": "发送通知", "sendNotificationHint": "通知客户关于此发货。", "toastCreated": "发货创建成功。"}, "fulfillment": {"cancelWarning": "您即将取消履行。此操作无法撤消。", "markAsDeliveredWarning": "您即将将履行标记为已送达。此操作无法撤消。", "differentOptionSelected": "所选的运输方式与客户选择的不同。", "disabledItemTooltip": "您选择的配送方式不允许配送此商品", "unfulfilledItems": "未履行商品", "statusLabel": "履行状态", "statusTitle": "履行状态", "fulfillItems": "履行商品", "awaitingFulfillmentBadge": "等待履行", "requiresShipping": "需要配送", "number": "履行 #{{number}}", "itemsToFulfill": "要履行的商品", "create": "创建履行", "available": "可用", "inStock": "有库存", "markAsShipped": "标记为已发货", "markAsPickedUp": "标记为已取走", "markAsDelivered": "标记为已送达", "itemsToFulfillDesc": "选择要履行的商品和数量", "locationDescription": "选择您想要从哪个位置履行商品。", "sendNotificationHint": "通知客户关于已创建的履行。", "methodDescription": "选择与客户所选不同的配送方式", "error": {"wrongQuantity": "只有一件商品可供履行", "wrongQuantity_other": "数量应该在 1 和 {{number}} 之间", "noItems": "没有要履行的商品。", "noShippingOption": "需要运输选项", "noLocation": "位置为必填项"}, "status": {"notFulfilled": "未履行", "partiallyFulfilled": "部分已履行", "fulfilled": "已履行", "partiallyShipped": "部分已发货", "shipped": "已发货", "delivered": "已送达", "partiallyDelivered": "部分已送达", "partiallyReturned": "部分已退货", "returned": "已退货", "canceled": "已取消", "requiresAction": "需要操作"}, "toast": {"created": "履行创建成功", "canceled": "履行已成功取消", "fulfillmentShipped": "无法取消已发货的履行", "fulfillmentDelivered": "履行已成功标记为已送达", "fulfillmentPickedUp": "订单已标记为已成功领取"}, "trackingLabel": "追踪", "shippingFromLabel": "发货地", "itemsLabel": "商品"}, "refund": {"title": "创建退款", "sendNotificationHint": "通知客户关于已创建的退款。", "systemPayment": "系统支付", "systemPaymentDesc": "您的一个或多个支付是系统支付。请注意，Medusa 不会处理此类支付的收取和退款。", "error": {"amountToLarge": "不能退款超过原始订单金额。", "amountNegative": "退款金额必须为正数。", "reasonRequired": "请选择退款原因。"}}, "customer": {"contactLabel": "联系方式", "editEmail": "编辑邮箱", "transferOwnership": "转移所有权", "editBillingAddress": "编辑账单地址", "editShippingAddress": "编辑配送地址"}, "activity": {"header": "活动", "showMoreActivities_one": "显示另外 {{count}} 个活动", "showMoreActivities_other": "显示另外 {{count}} 个活动", "comment": {"label": "评论", "placeholder": "留下评论", "addButtonText": "添加评论", "deleteButtonText": "删除评论"}, "from": "从", "to": "至", "events": {"common": {"toReturn": "要退回", "toSend": "要发送"}, "placed": {"title": "订单已下达", "fromSalesChannel": "来自 {{salesChannel}}"}, "canceled": {"title": "订单已取消"}, "payment": {"awaiting": "等待支付", "captured": "支付已收取", "canceled": "支付已取消", "refunded": "支付已退款"}, "fulfillment": {"created": "商品已履行", "canceled": "履行已取消", "shipped": "商品已发货", "delivered": "商品已送达", "items_one": "{{count}} 件商品", "items_other": "{{count}} 件商品"}, "return": {"created": "退货 #{{returnId}} 已请求", "canceled": "退货 #{{returnId}} 已取消", "received": "退货 #{{returnId}} 已收到", "items_one": "{{count}} 件商品已退回", "items_other": "{{count}} 件商品已退回"}, "note": {"comment": "评论", "byLine": "由 {{author}} 发表"}, "claim": {"created": "索赔 #{{claimId}} 已请求", "canceled": "索赔 #{{claimId}} 已取消", "itemsInbound": "{{count}} 件商品要退回", "itemsOutbound": "{{count}} 件商品要发送"}, "exchange": {"created": "换货 #{{exchangeId}} 已请求", "canceled": "换货 #{{exchangeId}} 已取消", "itemsInbound": "{{count}} 件商品要退回", "itemsOutbound": "{{count}} 件商品要发送"}, "edit": {"requested": "订单编辑 #{{editId}} 已请求", "confirmed": "订单编辑 #{{editId}} 已确认"}, "transfer": {"requested": "订单转移 #{{transferId}} 已请求", "confirmed": "订单转移 #{{transferId}} 已确认", "declined": "订单转移 #{{transferId}} 已拒绝"}, "update_order": {"shipping_address": "配送地址已更新", "billing_address": "账单地址已更新", "email": "邮箱已更新"}}}, "fields": {"displayId": "显示ID", "refundableAmount": "可退款金额", "returnableQuantity": "可退货数量"}}, "draftOrders": {"domain": "草稿订单", "deleteWarning": "您即将删除草稿订单 {{id}}。此操作无法撤销。", "paymentLinkLabel": "支付链接", "cartIdLabel": "购物车ID", "markAsPaid": {"label": "标记为已支付", "warningTitle": "标记为已支付", "warningDescription": "您即将将草稿订单标记为已支付。此操作无法撤销,之后将无法收取付款。"}, "status": {"open": "未完成", "completed": "已完成"}, "create": {"createDraftOrder": "创建草稿订单", "createDraftOrderHint": "创建新的草稿订单以在下单前管理订单详情。", "chooseRegionHint": "选择地区", "existingItemsLabel": "现有商品", "existingItemsHint": "将现有产品添加到草稿订单。", "customItemsLabel": "自定义商品", "customItemsHint": "将自定义商品添加到草稿订单。", "addExistingItemsAction": "添加现有商品", "addCustomItemAction": "添加自定义商品", "noCustomItemsAddedLabel": "尚未添加自定义商品", "noExistingItemsAddedLabel": "尚未添加现有商品", "chooseRegionTooltip": "请先选择地区", "useExistingCustomerLabel": "使用现有客户", "addShippingMethodsAction": "添加配送方式", "unitPriceOverrideLabel": "单价覆盖", "shippingOptionLabel": "配送选项", "shippingOptionHint": "为草稿订单选择配送选项。", "shippingPriceOverrideLabel": "配送价格覆盖", "shippingPriceOverrideHint": "覆盖草稿订单的配送价格。", "sendNotificationLabel": "发送通知", "sendNotificationHint": "在创建草稿订单时通知客户。"}, "validation": {"requiredEmailOrCustomer": "必须填写邮箱或客户信息。", "requiredItems": "至少需要一个商品。", "invalidEmail": "邮箱地址必须有效。"}}, "stockLocations": {"domain": "库存位置和配送", "list": {"description": "管理您商店的库存位置和配送选项。"}, "create": {"header": "创建库存位置", "hint": "库存位置是存储和发货产品的实体场所。", "successToast": "库存位置 {{name}} 创建成功。"}, "edit": {"header": "编辑库存位置", "viewInventory": "查看库存", "successToast": "库存位置 {{name}} 更新成功。"}, "delete": {"confirmation": "您即将删除库存位置 {{name}}。此操作无法撤销。"}, "fulfillmentProviders": {"header": "履约供应商", "shippingOptionsTooltip": "此下拉列表将仅包含为该位置启用的供应商。如果下拉列表被禁用,请将它们添加到该位置。", "label": "已连接的履约供应商", "connectedTo": "已连接 {{total}} 个履约供应商中的 {{count}} 个", "noProviders": "此库存位置未连接任何履约供应商。", "action": "连接供应商", "successToast": "库存位置的履约供应商更新成功。"}, "fulfillmentSets": {"pickup": {"header": "自提"}, "shipping": {"header": "配送"}, "disable": {"confirmation": "您确定要禁用 {{name}} 吗？这将删除所有相关的服务区域和配送选项,且无法撤销。", "pickup": "自提已成功禁用。", "shipping": "配送已成功禁用。"}, "enable": {"pickup": "自提已成功启用。", "shipping": "配送已成功启用。"}}, "sidebar": {"header": "配送配置", "shippingProfiles": {"label": "配送方案", "description": "根据配送要求对产品进行分组"}}, "salesChannels": {"header": "销售渠道", "label": "已连接的销售渠道", "connectedTo": "已连接 {{total}} 个销售渠道中的 {{count}} 个", "noChannels": "该位置未连接任何销售渠道。", "action": "连接销售渠道", "successToast": "销售渠道更新成功。", "hint": "管理与此位置相连的销售渠道。"}, "pickupOptions": {"edit": {"header": "编辑取货选项"}}, "shippingOptions": {"create": {"shipping": {"header": "为 {{zone}} 创建配送选项", "hint": "创建新的配送选项以定义如何从此位置发货产品。", "label": "配送选项", "successToast": "配送选项 {{name}} 创建成功。"}, "pickup": {"header": "为 {{zone}} 创建自提选项", "hint": "创建新的自提选项以定义如何从此位置自提产品。", "label": "自提选项", "successToast": "自提选项 {{name}} 创建成功。"}, "returns": {"header": "为 {{zone}} 创建退货选项", "hint": "创建新的退货选项以定义如何将产品退回到此位置。", "label": "退货选项", "successToast": "退货选项 {{name}} 创建成功。"}, "tabs": {"details": "详情", "prices": "价格"}, "action": "创建选项"}, "delete": {"confirmation": "您即将删除配送选项 {{name}}。此操作无法撤销。", "successToast": "配送选项 {{name}} 删除成功。"}, "edit": {"header": "编辑配送选项", "action": "编辑选项", "successToast": "配送选项 {{name}} 更新成功。"}, "pricing": {"action": "编辑价格"}, "conditionalPrices": {"header": "{{name}} 的条件价格", "description": "根据购物车商品总额管理此配送选项的条件价格。", "attributes": {"cartItemTotal": "购物车商品总额"}, "summaries": {"range": "如果 <0>{{attribute}}</0> 在 <1>{{gte}}</1> 和 <2>{{lte}}</2> 之间", "greaterThan": "如果 <0>{{attribute}}</0> ≥ <1>{{gte}}</1>", "lessThan": "如果 <0>{{attribute}}</0> ≤ <1>{{lte}}</1>"}, "actions": {"addPrice": "添加价格", "manageConditionalPrices": "管理条件价格"}, "rules": {"amount": "配送选项价格", "gte": "最低购物车商品总额", "lte": "最高购物车商品总额"}, "customRules": {"label": "自定义规则", "tooltip": "此条件价格具有无法在仪表板中管理的规则。", "eq": "购物车商品总额必须等于", "gt": "购物车商品总额必须大于", "lt": "购物车商品总额必须小于"}, "errors": {"amountRequired": "配送选项价格为必填项", "minOrMaxRequired": "必须提供最低或最高购物车商品总额中的至少一项", "minGreaterThanMax": "最低购物车商品总额必须小于或等于最高购物车商品总额", "duplicateAmount": "每个条件的配送选项价格必须唯一", "overlappingConditions": "所有价格规则中的条件必须唯一"}}, "fields": {"count": {"shipping_one": "{{count}} 个配送选项", "shipping_other": "{{count}} 个配送选项", "pickup_one": "{{count}} 个自提选项", "pickup_other": "{{count}} 个自提选项", "returns_one": "{{count}} 个退货选项", "returns_other": "{{count}} 个退货选项"}, "priceType": {"label": "价格类型", "options": {"fixed": {"label": "固定", "hint": "配送选项的价格是固定的,不会根据订单内容变化。"}, "calculated": {"label": "计算", "hint": "配送选项的价格由履约供应商在结账时计算。"}}}, "enableInStore": {"label": "在商店中启用", "hint": "客户是否可以在结账时使用此选项。"}, "provider": "履约供应商", "profile": "配送方案", "fulfillmentOption": "履约选项"}}, "serviceZones": {"create": {"headerPickup": "为 {{location}} 创建自提服务区域", "headerShipping": "为 {{location}} 创建配送服务区域", "action": "创建服务区域", "successToast": "服务区域 {{name}} 创建成功。"}, "edit": {"header": "编辑服务区域", "successToast": "服务区域 {{name}} 更新成功。"}, "delete": {"confirmation": "您即将删除服务区域 {{name}}。此操作无法撤销。", "successToast": "服务区域 {{name}} 删除成功。"}, "manageAreas": {"header": "管理 {{name}} 的区域", "action": "管理区域", "label": "区域", "hint": "选择服务区域覆盖的地理区域。", "successToast": "{{name}} 的区域更新成功。"}, "fields": {"noRecords": "没有可添加配送选项的服务区域。", "tip": "服务区域是地理区域或地区的集合。它用于将可用的配送选项限制在定义的位置集合中。"}}}, "shippingProfile": {"domain": "配送方案", "subtitle": "将具有相似配送要求的产品分组到方案中。", "create": {"header": "创建配送方案", "hint": "创建新的配送方案以对具有相似配送要求的产品进行分组。", "successToast": "配送方案 {{name}} 创建成功。"}, "delete": {"title": "删除配送方案", "description": "您即将删除配送方案 {{name}}。此操作无法撤销。", "successToast": "配送方案 {{name}} 删除成功。"}, "tooltip": {"type": "输入配送方案类型,例如：重型、超大、仅限货运等。"}}, "taxRegions": {"domain": "税收地区", "list": {"hint": "管理客户从不同国家和地区购物时的收费。"}, "delete": {"confirmation": "您即将删除税收地区。此操作无法撤销。", "successToast": "税收地区删除成功。"}, "create": {"header": "创建税收地区", "hint": "创建新的税收地区以定义特定国家的税率。", "errors": {"missingProvider": "创建税收地区时需要提供商。", "missingCountry": "创建税收地区时需要国家。"}, "successToast": "税收地区创建成功。"}, "edit": {"header": "编辑税收地区", "hint": "编辑税收地区详情。", "successToast": "税收地区更新成功。"}, "province": {"header": "省份", "create": {"header": "创建省份税收地区", "hint": "创建新的税收地区以定义特定省份的税率。"}}, "provider": {"header": "税务提供商"}, "state": {"header": "州", "create": {"header": "创建州税收地区", "hint": "创建新的税收地区以定义特定州的税率。"}}, "stateOrTerritory": {"header": "州或领地", "create": {"header": "创建州/领地税收地区", "hint": "创建新的税收地区以定义特定州/领地的税率。"}}, "county": {"header": "县", "create": {"header": "创建县税收地区", "hint": "创建新的税收地区以定义特定县的税率。"}}, "region": {"header": "地区", "create": {"header": "创建地区税收区域", "hint": "创建新的税收区域以定义特定地区的税率。"}}, "department": {"header": "部门", "create": {"header": "创建部门税收区域", "hint": "创建新的税收区域以定义特定部门的税率。"}}, "territory": {"header": "领地", "create": {"header": "创建领地税收区域", "hint": "创建新的税收区域以定义特定领地的税率。"}}, "prefecture": {"header": "县/府", "create": {"header": "创建县/府税收区域", "hint": "创建新的税收区域以定义特定县/府的税率。"}}, "district": {"header": "区", "create": {"header": "创建区税收区域", "hint": "创建新的税收区域以定义特定区的税率。"}}, "governorate": {"header": "省/州", "create": {"header": "创建省/州税收区域", "hint": "创建新的税收区域以定义特定省/州的税率。"}}, "canton": {"header": "州/县", "create": {"header": "创建州/县税收区域", "hint": "创建新的税收区域以定义特定州/县的税率。"}}, "emirate": {"header": "酋长国", "create": {"header": "创建酋长国税收区域", "hint": "创建新的税收区域以定义特定酋长国的税率。"}}, "sublevel": {"header": "子级", "create": {"header": "创建子级税收区域", "hint": "创建新的税收区域以定义特定子级的税率。"}}, "taxOverrides": {"header": "覆盖", "create": {"header": "创建覆盖", "hint": "创建一个税率以覆盖所选条件的默认税率。"}, "edit": {"header": "编辑覆盖", "hint": "编辑覆盖所选条件的默认税率的税率。"}}, "taxRates": {"create": {"header": "创建税率", "hint": "创建新的税率以定义地区的税率。", "successToast": "税率创建成功。"}, "edit": {"header": "编辑税率", "hint": "编辑税率以定义地区的税率。", "successToast": "税率更新成功。"}, "delete": {"confirmation": "您即将删除税率 {{name}}。此操作无法撤销。", "successToast": "税率删除成功。"}}, "fields": {"isCombinable": {"label": "可组合", "hint": "此税率是否可以与税收地区的默认税率组合。", "true": "可组合", "false": "不可组合"}, "defaultTaxRate": {"label": "默认税率", "tooltip": "此地区的默认税率。例如国家或地区的标准增值税率。", "action": "创建默认税率"}, "taxRate": "税率", "taxCode": "税收代码", "taxProvider": "税务提供商", "targets": {"label": "目标", "hint": "选择此税率将应用的目标。", "options": {"product": "产品", "productCollection": "产品系列", "productTag": "产品标签", "productType": "产品类型", "customerGroup": "客户组"}, "operators": {"in": "在", "on": "于", "and": "和"}, "placeholders": {"product": "搜索产品", "productCollection": "搜索产品系列", "productTag": "搜索产品标签", "productType": "搜索产品类型", "customerGroup": "搜索客户组"}, "tags": {"product": "产品", "productCollection": "产品系列", "productTag": "产品标签", "productType": "产品类型", "customerGroup": "客户组"}, "modal": {"header": "添加目标"}, "values_one": "{{count}} 个值", "values_other": "{{count}} 个值", "numberOfTargets_one": "{{count}} 个目标", "numberOfTargets_other": "{{count}} 个目标", "additionalValues_one": "还有 {{count}} 个值", "additionalValues_other": "还有 {{count}} 个值", "action": "添加目标"}, "sublevels": {"labels": {"province": "省", "state": "州", "region": "地区", "stateOrTerritory": "州/领地", "department": "部门", "county": "县", "territory": "领地", "prefecture": "县/府", "district": "区", "governorate": "省/州", "emirate": "酋长国", "canton": "州/县", "sublevel": "子级代码"}, "placeholders": {"province": "选择省", "state": "选择州", "region": "选择地区", "stateOrTerritory": "选择州/领地", "department": "选择部门", "county": "选择县", "territory": "选择领地", "prefecture": "选择县/府", "district": "选择区", "governorate": "选择省/州", "emirate": "选择酋长国", "canton": "选择州/县"}, "tooltips": {"sublevel": "输入子级税收地区的 ISO 3166-2 代码。", "notPartOfCountry": "{{province}} 似乎不属于 {{country}}。请仔细检查是否正确。"}, "alert": {"header": "此税收地区已禁用子级地区", "description": "默认情况下,此地区已禁用子级地区。您可以启用它们以创建如省、州或领地等子级地区。", "action": "启用子级地区"}}, "noDefaultRate": {"label": "无默认税率", "tooltip": "此税收地区没有默认税率。如果有标准税率(如国家增值税),请将其添加到此地区。"}}}, "promotions": {"domain": "促销活动", "sections": {"details": "促销详情"}, "tabs": {"template": "类型", "details": "详情", "campaign": "营销活动"}, "fields": {"type": "类型", "value_type": "价值类型", "value": "价值", "campaign": "营销活动", "method": "方式", "allocation": "分配", "addCondition": "添加条件", "clearAll": "清除全部", "amount": {"tooltip": "选择货币代码以启用金额设置"}, "conditions": {"rules": {"title": "谁可以使用此代码？", "description": "哪些客户可以使用促销代码？如果不设置限制,所有客户都可以使用促销代码。"}, "target-rules": {"title": "促销将应用于哪些商品？", "description": "促销将应用于符合以下条件的商品。"}, "buy-rules": {"title": "购物车需要满足什么条件才能解锁促销？", "description": "如果满足这些条件,我们将在目标商品上启用促销。"}}}, "tooltips": {"campaignType": "必须在促销中选择货币代码才能设置消费预算。"}, "errors": {"requiredField": "必填字段", "promotionTabError": "继续前请修复促销选项卡中的错误"}, "toasts": {"promotionCreateSuccess": "促销活动 ({{code}}) 创建成功。"}, "create": {}, "edit": {"title": "编辑促销详情", "rules": {"title": "编辑使用条件"}, "target-rules": {"title": "编辑商品条件"}, "buy-rules": {"title": "编辑购买规则"}}, "campaign": {"header": "营销活动", "edit": {"header": "编辑营销活动", "successToast": "促销活动的营销活动更新成功。"}, "actions": {"goToCampaign": "前往营销活动"}}, "campaign_currency": {"tooltip": "这是促销活动的货币。可以在详情选项卡中更改。"}, "form": {"required": "必填", "and": "且", "selectAttribute": "选择属性", "campaign": {"existing": {"title": "现有营销活动", "description": "将促销添加到现有营销活动。", "placeholder": {"title": "没有现有营销活动", "desc": "您可以创建一个营销活动来跟踪多个促销并设置预算限制。"}}, "new": {"title": "新建营销活动", "description": "为此促销创建新的营销活动。"}, "none": {"title": "不使用营销活动", "description": "继续而不将促销与营销活动关联"}}, "status": {"label": "状态", "draft": {"title": "草稿", "description": "客户暂时无法使用此优惠码"}, "active": {"title": "启用", "description": "客户可以使用此优惠码"}, "inactive": {"title": "停用", "description": "客户将无法使用此优惠码"}}, "method": {"label": "方式", "code": {"title": "促销代码", "description": "客户必须在结账时输入此代码"}, "automatic": {"title": "自动", "description": "客户将在结账时看到此促销"}}, "max_quantity": {"title": "最大数量", "description": "此促销适用的最大商品数量。"}, "type": {"standard": {"title": "标准", "description": "标准促销"}, "buyget": {"title": "买赠", "description": "买 X 赠 Y 促销"}}, "allocation": {"each": {"title": "每个", "description": "对每个商品应用价值"}, "across": {"title": "跨商品", "description": "跨商品应用价值"}}, "code": {"title": "代码", "description": "客户将在结账时输入的代码。"}, "value": {"title": "促销价值"}, "value_type": {"fixed": {"title": "促销价值", "description": "要折扣的金额。例如：100"}, "percentage": {"title": "促销价值", "description": "要折扣的百分比。例如：8%"}}}, "deleteWarning": "您即将删除促销 {{code}}。此操作无法撤销。", "createPromotionTitle": "创建促销", "type": "促销类型", "conditions": {"add": "添加条件", "list": {"noRecordsMessage": "添加条件以限制促销适用的商品。"}}}, "campaigns": {"domain": "营销活动", "details": "营销活动详情", "status": {"active": "活跃", "expired": "已过期", "scheduled": "已计划"}, "delete": {"title": "确定要删除吗？", "description": "您即将删除营销活动 '{{name}}'。此操作无法撤销。", "successToast": "营销活动 '{{name}}' 创建成功。"}, "edit": {"header": "编辑营销活动", "description": "编辑营销活动的详细信息。", "successToast": "营销活动 '{{name}}' 更新成功。"}, "configuration": {"header": "配置", "edit": {"header": "编辑营销活动配置", "description": "编辑营销活动的配置。", "successToast": "营销活动配置更新成功。"}}, "create": {"title": "创建营销活动", "description": "创建营销活动。", "hint": "创建营销活动。", "header": "创建营销活动", "successToast": "营销活动 '{{name}}' 创建成功。"}, "fields": {"name": "名称", "identifier": "标识符", "start_date": "开始日期", "end_date": "结束日期", "total_spend": "预算花费", "total_used": "预算使用", "budget_limit": "预算限制", "campaign_id": {"hint": "只有与促销相同的货币代码的营销活动才会显示在此列表中。"}}, "budget": {"create": {"hint": "创建营销活动的预算。", "header": "营销活动预算"}, "details": "营销活动预算", "fields": {"type": "类型", "currency": "货币", "limit": "限制", "used": "已使用"}, "type": {"spend": {"title": "花费", "description": "设置促销使用的总折扣金额限制。"}, "usage": {"title": "使用", "description": "设置促销可以使用的次数限制。"}}, "edit": {"header": "编辑营销活动预算"}}, "promotions": {"remove": {"title": "从营销活动中移除促销", "description": "您即将从营销活动中移除 {{count}} 个促销。此操作无法撤销。"}, "alreadyAdded": "此促销已添加到营销活动。", "alreadyAddedDiffCampaign": "此促销已添加到不同的营销活动 ({{name}})。", "currencyMismatch": "促销和营销活动的货币不匹配", "toast": {"success": "成功添加 {{count}} 个促销到营销活动"}, "add": {"list": {"noRecordsMessage": "首先创建促销。"}}, "list": {"noRecordsMessage": "营销活动中没有促销。"}}, "deleteCampaignWarning": "您即将删除营销活动 {{name}}。此操作无法撤销。", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "价格列表", "subtitle": "创建销售或覆盖特定条件的定价。", "delete": {"confirmation": "您即将删除价格列表 {{title}}。此操作无法撤销。", "successToast": "价格列表 {{title}} 删除成功。"}, "create": {"header": "创建价格列表", "subheader": "创建新的价格列表以管理您的产品价格。", "tabs": {"details": "详情", "products": "产品", "prices": "价格"}, "successToast": "价格列表 {{title}} 创建成功。", "products": {"list": {"noRecordsMessage": "首先创建产品。"}}}, "edit": {"header": "编辑价格列表", "successToast": "价格列表 {{title}} 更新成功。"}, "configuration": {"header": "配置", "edit": {"header": "编辑价格列表配置", "description": "编辑价格列表的配置。", "successToast": "价格列表配置更新成功。"}}, "products": {"header": "产品", "actions": {"addProducts": "添加产品", "editPrices": "编辑价格"}, "delete": {"confirmation_one": "您即将删除价格列表中 {{count}} 个产品的价格。此操作无法撤销。", "confirmation_other": "您即将删除价格列表中 {{count}} 个产品的价格。此操作无法撤销。", "successToast_one": "成功删除价格列表中 {{count}} 个产品的价格。", "successToast_other": "成功删除价格列表中 {{count}} 个产品的价格。"}, "add": {"successToast": "价格列表中成功添加价格。"}, "edit": {"successToast": "价格列表中成功更新价格。"}}, "fields": {"priceOverrides": {"label": "价格覆盖", "header": "价格覆盖"}, "status": {"label": "状态", "options": {"active": "活跃", "draft": "草稿", "expired": "已过期", "scheduled": "已计划"}}, "type": {"label": "类型", "hint": "选择要创建的价格列表类型。", "options": {"sale": {"label": "销售", "description": "销售价格是临时价格变化。"}, "override": {"label": "覆盖", "description": "覆盖通常用于创建客户特定的价格。"}}}, "startsAt": {"label": "价格列表有开始日期？", "hint": "计划价格列表在将来激活。"}, "endsAt": {"label": "价格列表有到期日期？", "hint": "计划价格列表在将来失效。"}, "customerAvailability": {"header": "选择客户组", "label": "客户可用性", "hint": "选择哪些客户组的价格列表应该应用。", "placeholder": "搜索客户组", "attribute": "客户组"}}}, "profile": {"domain": "个人资料", "manageYourProfileDetails": "管理您的个人资料详细信息。", "fields": {"languageLabel": "语言", "usageInsightsLabel": "使用见解"}, "edit": {"header": "编辑个人资料", "languageHint": "您希望在管理仪表板中使用的语言。这不会更改您的商店的语言。", "languagePlaceholder": "选择语言", "usageInsightsHint": "分享使用见解并帮助我们改进 Medusa。您可以在我们的 <0>文档</0> 中了解更多关于我们收集和使用数据的信息。"}, "toast": {"edit": "个人资料更改已保存"}}, "users": {"domain": "用户", "editUser": "编辑用户", "inviteUser": "邀请用户", "inviteUserHint": "邀请新用户到您的商店。", "sendInvite": "发送邀请", "pendingInvites": "待处理邀请", "deleteInviteWarning": "您即将删除邀请 {{email}}。此操作无法撤销。", "resendInvite": "重新发送邀请", "copyInviteLink": "复制邀请链接", "expiredOnDate": "已过期 {{date}}", "validFromUntil": "有效期 <0>{{from}}</0> - <1>{{until}}</1>", "acceptedOnDate": "已接受 {{date}}", "inviteStatus": {"accepted": "已接受", "pending": "待处理", "expired": "已过期"}, "roles": {"admin": "管理员", "developer": "开发者", "member": "成员"}, "list": {"empty": {"heading": "暂无用户", "description": "邀请用户后，他们将会显示在这里。"}, "filtered": {"heading": "无搜索结果", "description": "没有符合当前筛选条件的用户。"}}, "deleteUserWarning": "您即将删除用户 {{name}}。此操作无法撤销。", "deleteUserSuccess": "用户 {{name}} 删除成功。", "invite": "邀请"}, "store": {"domain": "商店", "manageYourStoresDetails": "管理您的商店详细信息", "editStore": "编辑商店", "defaultCurrency": "默认货币", "defaultRegion": "默认地区", "defaultSalesChannel": "默认销售渠道", "defaultLocation": "默认位置", "swapLinkTemplate": "交换链接模板", "paymentLinkTemplate": "支付链接模板", "inviteLinkTemplate": "邀请链接模板", "currencies": "货币", "addCurrencies": "添加货币", "enableTaxInclusivePricing": "启用含税定价", "disableTaxInclusivePricing": "禁用含税定价", "removeCurrencyWarning_one": "您即将从您的商店中移除 {{count}} 个货币。确保您已从所有价格中移除该货币。", "removeCurrencyWarning_other": "您即将从您的商店中移除 {{count}} 个货币。确保您已从所有价格中移除该货币。", "currencyAlreadyAdded": "该货币已添加到您的商店。", "edit": {"header": "编辑商店"}, "toast": {"update": "商店更新成功", "currenciesUpdated": "货币更新成功", "currenciesRemoved": "从商店中成功移除货币", "updatedTaxInclusivitySuccessfully": "含税定价更新成功"}}, "regions": {"domain": "地区", "subtitle": "地区是您销售产品的区域。它可以覆盖多个国家,并且有不同的税率、供应商和货币。", "createRegion": "创建地区", "createRegionHint": "管理税收率和供应商的地区。", "addCountries": "添加国家", "editRegion": "编辑地区", "countriesHint": "添加此地区包含的国家。", "deleteRegionWarning": "您即将删除地区 {{name}}。此操作无法撤销。", "removeCountriesWarning_one": "您即将从地区中移除 {{count}} 个国家。此操作无法撤销。", "removeCountriesWarning_other": "您即将从地区中移除 {{count}} 个国家。此操作无法撤销。", "removeCountryWarning": "您即将从地区中移除国家 {{name}}。此操作无法撤销。", "automaticTaxesHint": "当启用时,税收将仅基于运输地址在结账时计算。", "taxInclusiveHint": "当启用时,地区的价格将包含税收。", "providersHint": "添加此地区可用的支付提供商。", "shippingOptions": "运输选项", "deleteShippingOptionWarning": "您即将删除运输选项 {{name}}。此操作无法撤销。", "return": "返回", "outbound": "出站", "priceType": "价格类型", "flatRate": "固定费率", "calculated": "计算", "list": {"noRecordsMessage": "创建地区以销售您的产品。"}, "toast": {"delete": "地区删除成功", "edit": "地区编辑已保存", "create": "地区创建成功", "countries": "地区国家更新成功"}, "shippingOption": {"createShippingOption": "创建运输选项", "createShippingOptionHint": "为地区创建新的运输选项。", "editShippingOption": "编辑运输选项", "fulfillmentMethod": "履行方法", "type": {"outbound": "出站", "outboundHint": "使用此选项如果您正在创建运输选项以将产品发送给客户。", "return": "返回", "returnHint": "使用此选项如果您正在创建运输选项以将产品返回给您。"}, "priceType": {"label": "价格类型", "flatRate": "固定费率", "calculated": "计算"}, "availability": {"adminOnly": "仅管理员", "adminOnlyHint": "当启用时,运输选项将仅在管理仪表板中可用,而不会在商店前端可用。"}, "taxInclusiveHint": "当启用时,运输选项的价格将包含税收。", "requirements": {"label": "要求", "hint": "指定运输选项的要求。"}}}, "taxes": {"domain": "税收地区", "domainDescription": "管理您的税收地区", "countries": {"taxCountriesHint": "税收设置适用于列出的国家。"}, "settings": {"editTaxSettings": "编辑税收设置", "taxProviderLabel": "税收提供商", "systemTaxProviderLabel": "系统税收提供商", "calculateTaxesAutomaticallyLabel": "自动计算税收", "calculateTaxesAutomaticallyHint": "当启用时,税收将自动计算并应用于购物车。当禁用时,税收必须手动计算在结账时。手动税收推荐用于与第三方税收提供商一起使用。", "applyTaxesOnGiftCardsLabel": "对礼品卡征税", "applyTaxesOnGiftCardsHint": "当启用时,税收将应用于礼品卡在结账时。在某些国家,税收法规要求对礼品卡征税。", "defaultTaxRateLabel": "默认税率", "defaultTaxCodeLabel": "默认税收代码"}, "defaultRate": {"sectionTitle": "默认税率"}, "taxRate": {"sectionTitle": "税率", "createTaxRate": "创建税率", "createTaxRateHint": "为地区创建新的税率。", "deleteRateDescription": "您即将删除税率 {{name}}。此操作无法撤销。", "editRateAction": "编辑费率", "editOverridesAction": "编辑覆盖", "editOverridesTitle": "编辑税率覆盖", "editOverridesHint": "指定税率覆盖。", "deleteTaxRateWarning": "您即将删除税率 {{name}}。此操作无法撤销。", "productOverridesLabel": "产品覆盖", "productOverridesHint": "指定产品覆盖税率。", "addProductOverridesAction": "添加产品覆盖", "productTypeOverridesLabel": "产品类型覆盖", "productTypeOverridesHint": "指定产品类型覆盖税率。", "addProductTypeOverridesAction": "添加产品类型覆盖", "shippingOptionOverridesLabel": "运输选项覆盖", "shippingOptionOverridesHint": "指定运输选项覆盖税率。", "addShippingOptionOverridesAction": "添加运输选项覆盖", "productOverridesHeader": "产品", "productTypeOverridesHeader": "产品类型", "shippingOptionOverridesHeader": "运输选项"}}, "locations": {"domain": "位置", "editLocation": "编辑位置", "addSalesChannels": "添加销售渠道", "noLocationsFound": "没有找到位置", "selectLocations": "选择库存商品的位置。", "deleteLocationWarning": "您即将删除位置 {{name}}。此操作无法撤销。", "removeSalesChannelsWarning_one": "您即将从位置中移除 {{count}} 个销售渠道。", "removeSalesChannelsWarning_other": "您即将从位置中移除 {{count}} 个销售渠道。", "toast": {"create": "位置创建成功", "update": "位置更新成功", "removeChannel": "销售渠道移除成功"}}, "reservations": {"domain": "预订", "subtitle": "管理库存商品的预订数量。", "deleteWarning": "您即将删除预订。此操作无法撤销。"}, "salesChannels": {"domain": "销售渠道", "subtitle": "管理在线和离线销售渠道。", "list": {"empty": {"heading": "未找到销售渠道", "description": "创建销售渠道后，它将显示在这里。"}, "filtered": {"heading": "无搜索结果", "description": "没有符合当前筛选条件的销售渠道。"}}, "createSalesChannel": "创建销售渠道", "createSalesChannelHint": "创建新的销售渠道以销售您的产品。", "enabledHint": "指定销售渠道是否启用。", "removeProductsWarning_one": "您即将从 {{sales_channel}} 中移除 {{count}} 个产品。", "removeProductsWarning_other": "您即将从 {{sales_channel}} 中移除 {{count}} 个产品。", "addProducts": "添加产品", "editSalesChannel": "编辑销售渠道", "productAlreadyAdded": "该产品已添加到销售渠道。", "deleteSalesChannelWarning": "您即将删除销售渠道 {{name}}。此操作无法撤销。", "toast": {"create": "销售渠道创建成功", "update": "销售渠道更新成功", "delete": "销售渠道删除成功"}, "tooltip": {"cannotDeleteDefault": "无法删除默认销售渠道"}, "products": {"list": {"noRecordsMessage": "销售渠道中没有产品。"}, "add": {"list": {"noRecordsMessage": "首先创建产品。"}}}}, "apiKeyManagement": {"domain": {"publishable": "发布API密钥", "secret": "秘密API密钥"}, "subtitle": {"publishable": "管理商店前端限制请求范围的API密钥。", "secret": "管理管理仪表板认证管理员用户的API密钥。"}, "status": {"active": "活跃", "revoked": "已撤销"}, "type": {"publishable": "发布", "secret": "秘密"}, "create": {"createPublishableHeader": "创建发布API密钥", "createPublishableHint": "创建新的发布API密钥以限制请求范围到特定的销售渠道。", "createSecretHeader": "创建秘密API密钥", "createSecretHint": "创建新的秘密API密钥以访问Medusa API作为认证的管理员用户。", "secretKeyCreatedHeader": "秘密密钥已创建", "secretKeyCreatedHint": "您的新的秘密密钥已生成。请立即复制并安全存储。这是唯一一次显示。", "copySecretTokenSuccess": "秘密密钥已复制到剪贴板。", "copySecretTokenFailure": "无法将秘密密钥复制到剪贴板。", "successToast": "API密钥创建成功。"}, "edit": {"header": "编辑API密钥", "description": "编辑API密钥的标题。", "successToast": "API密钥 {{title}} 更新成功。"}, "salesChannels": {"title": "添加销售渠道", "description": "将销售渠道添加到API密钥应限制到的销售渠道。", "successToast_one": "{{count}} 个销售渠道已成功添加到API密钥。", "successToast_other": "{{count}} 个销售渠道已成功添加到API密钥。", "alreadyAddedTooltip": "销售渠道已添加到API密钥。", "list": {"noRecordsMessage": "发布API密钥范围内没有销售渠道。"}}, "delete": {"warning": "您即将删除API密钥 {{title}}。此操作无法撤销。", "successToast": "API密钥 {{title}} 删除成功。"}, "revoke": {"warning": "您即将撤销API密钥 {{title}}。此操作无法撤销。", "successToast": "API密钥 {{title}} 撤销成功。"}, "addSalesChannels": {"list": {"noRecordsMessage": "首先创建销售渠道。"}}, "removeSalesChannel": {"warning": "您即将从API密钥中移除销售渠道 {{name}}。此操作无法撤销。", "warningBatch_one": "您即将从API密钥中移除 {{count}} 个销售渠道。此操作无法撤销。", "warningBatch_other": "您即将从API密钥中移除 {{count}} 个销售渠道。此操作无法撤销。", "successToast": "销售渠道已成功从API密钥中移除。", "successToastBatch_one": "{{count}} 个销售渠道已成功从API密钥中移除。", "successToastBatch_other": "{{count}} 个销售渠道已成功从API密钥中移除。"}, "actions": {"revoke": "撤销API密钥", "copy": "复制API密钥", "copySuccessToast": "API密钥已复制到剪贴板。"}, "table": {"lastUsedAtHeader": "上次使用时间", "createdAtHeader": "撤销时间"}, "fields": {"lastUsedAtLabel": "上次使用时间", "revokedByLabel": "撤销者", "revokedAtLabel": "撤销时间", "createdByLabel": "创建者"}}, "returnReasons": {"domain": "退货原因", "subtitle": "管理退货的原因。", "calloutHint": "管理分类退货的原因。", "editReason": "编辑退货原因", "create": {"header": "添加退货原因", "subtitle": "指定退货的最常见原因。", "hint": "创建新的退货原因以分类退货。", "successToast": "退货原因 {{label}} 创建成功。"}, "edit": {"header": "编辑退货原因", "subtitle": "编辑退货原因的值。", "successToast": "退货原因 {{label}} 更新成功。"}, "delete": {"confirmation": "您即将删除退货原因 {{label}}。此操作无法撤销。", "successToast": "退货原因 {{label}} 删除成功。"}, "fields": {"value": {"label": "值", "placeholder": "wrong_size", "tooltip": "值应该是退货原因的唯一标识符。"}, "label": {"label": "标签", "placeholder": "错误尺寸"}, "description": {"label": "描述", "placeholder": "客户收到错误尺寸"}}}, "login": {"forgotPassword": "忘记密码？ - <0>重置</0>", "title": "欢迎使用Medusa", "hint": "登录以访问账户区域"}, "invite": {"title": "欢迎使用Medusa", "hint": "在下面创建您的账户", "backToLogin": "返回登录", "createAccount": "创建账户", "alreadyHaveAccount": "已经有账户？ - <0>登录</0>", "emailTooltip": "您的电子邮件无法更改。如果您想使用另一个电子邮件,必须发送新的邀请。", "invalidInvite": "邀请无效或已过期。", "successTitle": "您的账户已注册", "successHint": "立即开始使用Medusa Admin。", "successAction": "开始<PERSON><PERSON><PERSON>", "invalidTokenTitle": "您的邀请令牌无效", "invalidTokenHint": "尝试请求新的邀请链接。", "passwordMismatch": "密码不匹配", "toast": {"accepted": "邀请已成功接受"}}, "resetPassword": {"title": "重置密码", "hint": "在下面输入您的电子邮件,我们将向您发送如何重置密码的说明。", "email": "电子邮件", "sendResetInstructions": "发送重置说明", "backToLogin": "<0>返回登录</0>", "newPasswordHint": "在下面选择新密码。", "invalidTokenTitle": "您的重置令牌无效", "invalidTokenHint": "尝试请求新的重置链接。", "expiredTokenTitle": "您的重置令牌已过期", "goToResetPassword": "转到重置密码", "resetPassword": "重置密码", "newPassword": "新密码", "repeatNewPassword": "重复新密码", "tokenExpiresIn": "令牌将在 <0>{{time}}</0> 分钟后过期", "successfulRequestTitle": "已成功向您发送邮件", "successfulRequest": "我们已向您发送一封可用于重置密码的邮件。如果几分钟后仍未收到,请检查垃圾邮件文件夹。", "successfulResetTitle": "密码重置成功", "successfulReset": "请在登录页面登录。", "passwordMismatch": "密码不匹配", "invalidLinkTitle": "您的重置链接无效", "invalidLinkHint": "尝试再次重置密码。"}, "workflowExecutions": {"domain": "工作流", "subtitle": "查看和跟踪您的 Medusa 应用程序中的工作流执行情况。", "transactionIdLabel": "交易 ID", "workflowIdLabel": "工作流 ID", "progressLabel": "进度", "stepsCompletedLabel_one": "已完成 {{completed}}/{{count}} 步", "stepsCompletedLabel_other": "已完成 {{completed}}/{{count}} 步", "list": {"noRecordsMessage": "尚未执行任何工作流。"}, "history": {"sectionTitle": "历史", "runningState": "运行中...", "awaitingState": "等待中", "failedState": "失败", "skippedState": "已跳过", "skippedFailureState": "已跳过(失败)", "definitionLabel": "定义", "outputLabel": "输出", "compensateInputLabel": "补偿输入", "revertedLabel": "已回滚", "errorLabel": "错误"}, "state": {"done": "完成", "failed": "失败", "reverted": "已回滚", "invoking": "调用中", "compensating": "补偿中", "notStarted": "未开始"}, "transaction": {"state": {"waitingToCompensate": "等待补偿"}}, "step": {"state": {"skipped": "已跳过", "skippedFailure": "已跳过(失败)", "dormant": "休眠", "timeout": "超时"}}}, "productTypes": {"domain": "产品类型", "subtitle": "将您的产品组织成类型。", "create": {"header": "创建产品类型", "hint": "创建新的产品类型以对产品进行分类。", "successToast": "产品类型 {{value}} 创建成功。"}, "edit": {"header": "编辑产品类型", "successToast": "产品类型 {{value}} 更新成功。"}, "delete": {"confirmation": "您即将删除产品类型 {{value}}。此操作无法撤销。", "successToast": "产品类型 {{value}} 删除成功。"}, "fields": {"value": "值"}}, "productTags": {"domain": "产品标签", "create": {"header": "创建产品标签", "subtitle": "创建新的产品标签以对产品进行分类。", "successToast": "产品标签 {{value}} 创建成功。"}, "edit": {"header": "编辑产品标签", "subtitle": "编辑产品标签的值。", "successToast": "产品标签 {{value}} 更新成功。"}, "delete": {"confirmation": "您即将删除产品标签 {{value}}。此操作无法撤销。", "successToast": "产品标签 {{value}} 删除成功。"}, "fields": {"value": "值"}}, "notifications": {"domain": "通知", "emptyState": {"title": "无通知", "description": "您目前没有任何通知,但一旦有通知它们将显示在这里。"}, "accessibility": {"description": "关于 Medusa 活动的通知将在此处列出。"}}, "errors": {"serverError": "服务器错误 - 请稍后重试。", "invalidCredentials": "电子邮件或密码错误"}, "statuses": {"scheduled": "已计划", "expired": "已过期", "active": "活跃", "inactive": "不活跃", "draft": "草稿", "enabled": "已启用", "disabled": "已禁用"}, "labels": {"productVariant": "产品变体", "prices": "价格", "available": "可用", "inStock": "有库存", "added": "已添加", "removed": "已移除", "from": "从", "to": "至", "beaware": "请注意", "loading": "加载中"}, "fields": {"amount": "金额", "refundAmount": "退款金额", "name": "名称", "default": "默认", "lastName": "姓", "firstName": "名", "title": "标题", "customTitle": "自定义标题", "manageInventory": "管理库存", "inventoryKit": "有库存套件", "inventoryItems": "库存商品", "inventoryItem": "库存商品", "requiredQuantity": "所需数量", "description": "描述", "email": "电子邮件", "password": "密码", "repeatPassword": "重复密码", "confirmPassword": "确认密码", "newPassword": "新密码", "repeatNewPassword": "重复新密码", "categories": "分类", "shippingMethod": "配送方式", "configurations": "配置", "conditions": "条件", "category": "分类", "collection": "系列", "discountable": "可打折", "handle": "标识", "subtitle": "副标题", "by": "由", "item": "商品", "qty": "数量", "limit": "限制", "tags": "标签", "type": "类型", "reason": "原因", "none": "无", "all": "全部", "search": "搜索", "percentage": "百分比", "sales_channels": "销售渠道", "customer_groups": "客户组", "product_tags": "产品标签", "product_types": "产品类型", "product_collections": "产品系列", "status": "状态", "code": "代码", "value": "值", "disabled": "已禁用", "dynamic": "动态", "normal": "普通", "years": "年", "months": "月", "days": "天", "hours": "小时", "minutes": "分钟", "totalRedemptions": "总兑换次数", "countries": "国家", "paymentProviders": "支付供应商", "refundReason": "退款原因", "fulfillmentProviders": "履约供应商", "fulfillmentProvider": "履约供应商", "providers": "供应商", "availability": "可用性", "inventory": "库存", "optional": "可选", "note": "备注", "automaticTaxes": "自动费", "taxInclusivePricing": "含税定价", "currency": "货币", "address": "地址", "address2": "公寓、套房等", "city": "城市", "postalCode": "邮政编码", "country": "国家", "state": "州/省", "province": "省", "company": "公司", "phone": "电话", "metadata": "元数据", "selectCountry": "选择国家", "products": "产品", "variants": "变体", "orders": "订单", "account": "账户", "total": "订单总额", "paidTotal": "已收金额", "totalExclTax": "不含税总额", "subtotal": "小计", "shipping": "配送", "outboundShipping": "出站配送", "returnShipping": "退货配送", "tax": "税费", "created": "已创建", "key": "键", "customer": "客户", "date": "日期", "order": "订单", "fulfillment": "履约", "provider": "供应商", "payment": "支付", "items": "商品", "salesChannel": "销售渠道", "region": "地区", "discount": "折扣", "role": "角色", "sent": "已发送", "salesChannels": "销售渠道", "product": "产品", "createdAt": "创建时间", "updatedAt": "更新时间", "revokedAt": "撤销时间", "true": "是", "false": "否", "giftCard": "礼品卡", "tag": "标签", "dateIssued": "发行日期", "issuedDate": "发行日期", "expiryDate": "过期日期", "price": "价格", "priceTemplate": "价格 {{regionOrCurrency}}", "height": "高度", "width": "宽度", "length": "长度", "weight": "重量", "midCode": "MID 代码", "hsCode": "HS 代码", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "库存数量", "barcode": "条形码", "countryOfOrigin": "原产国", "material": "材料", "thumbnail": "缩略图", "sku": "SKU", "managedInventory": "管理库存", "allowBackorder": "允许缺货订购", "inStock": "有库存", "location": "位置", "quantity": "数量", "variant": "变体", "id": "ID", "parent": "父级", "minSubtotal": "最低小计", "maxSubtotal": "最高小计", "shippingProfile": "配送方案", "summary": "摘要", "details": "详情", "label": "标签", "rate": "费率", "requiresShipping": "需要配送", "unitPrice": "单价", "startDate": "开始日期", "endDate": "结束日期", "draft": "草稿", "values": "值"}, "dateTime": {"years_one": "年", "years_other": "年", "months_one": "月", "months_other": "月", "weeks_one": "周", "weeks_other": "周", "days_one": "天", "days_other": "天", "hours_one": "小时", "hours_other": "小时", "minutes_one": "分钟", "minutes_other": "分钟", "seconds_one": "秒", "seconds_other": "秒"}, "quotes": {"domain": "报价", "title": "报价", "subtitle": "管理客户报价和提案", "noQuotes": "暂无记录可显示", "noQuotesDescription": "当前没有报价。请从店面创建一个。", "table": {"id": "报价号", "customer": "客户", "status": "状态", "company": "公司", "amount": "金额", "createdAt": "创建时间", "updatedAt": "更新时间", "actions": "操作"}, "filters": {"status": "按状态筛选"}, "status": {"pending_merchant": "等待商家处理", "pending_customer": "等待客户回复", "merchant_rejected": "商家已拒绝", "customer_rejected": "客户已拒绝", "accepted": "已接受", "unknown": "未知状态"}, "actions": {"sendQuote": "发送报价", "rejectQuote": "拒绝报价", "viewOrder": "查看订单"}, "details": {"header": "报价详情", "quoteSummary": "报价摘要", "customer": "客户", "company": "公司", "items": "商品", "total": "总计", "subtotal": "小计", "shipping": "运费", "tax": "税费", "discounts": "折扣", "originalTotal": "原始总计", "quoteTotal": "报价总计", "messages": "消息", "actions": "操作", "sendMessage": "发送消息", "send": "发送", "pickQuoteItem": "选择报价项目", "selectQuoteItem": "选择一个报价项目进行评论", "selectItem": "选择项目", "manage": "管理", "phone": "电话", "spendingLimit": "消费限额", "name": "名称", "manageQuote": "管理报价", "noItems": "此报价中没有商品", "noMessages": "此报价没有消息"}, "items": {"title": "产品", "quantity": "数量", "unitPrice": "单价", "total": "总计"}, "messages": {"admin": "管理员", "customer": "客户", "placeholder": "在此输入您的消息..."}, "confirmations": {"sendTitle": "发送报价", "sendDescription": "确定要将此报价发送给客户吗？", "rejectTitle": "拒绝报价", "rejectDescription": "确定要拒绝此报价吗？"}, "acceptance": {"message": "报价已被接受"}, "toasts": {"sendSuccess": "报价发送成功", "sendError": "发送报价失败", "rejectSuccess": "报价拒绝成功", "rejectError": "拒绝报价失败", "messageSuccess": "消息发送成功", "messageError": "发送消息失败", "updateSuccess": "报价更新成功"}, "manage": {"overridePriceHint": "覆盖此商品的原始价格", "updatePrice": "更新价格"}}, "companies": {"domain": "公司", "title": "公司", "subtitle": "管理业务关系", "noCompanies": "未找到公司", "noCompaniesDescription": "创建您的第一个公司以开始。", "notFound": "公司未找到", "table": {"name": "公司名称", "phone": "电话", "email": "邮箱", "address": "地址", "employees": "员工数", "customerGroup": "客户群组", "actions": "操作"}, "fields": {"name": "公司名称", "email": "邮箱", "phone": "电话", "website": "网站", "address": "地址", "city": "城市", "state": "省/州", "zip": "邮编", "zipCode": "邮编", "country": "国家", "currency": "货币", "logoUrl": "Logo URL", "description": "描述", "employees": "员工", "customerGroup": "客户群组", "approvalSettings": "审批设置", "namePlaceholder": "请输入公司名称", "emailPlaceholder": "请输入邮箱地址", "phonePlaceholder": "请输入电话号码", "addressPlaceholder": "请输入地址", "selectCountry": "选择国家", "selectCurrency": "选择货币"}, "placeholders": {"name": "输入公司名称", "email": "输入邮箱地址", "phone": "输入电话号码", "website": "输入网站URL", "address": "输入街道地址", "city": "输入城市", "state": "输入省/州", "zip": "输入邮编", "logoUrl": "输入Logo URL", "description": "输入公司描述", "selectCountry": "选择国家", "selectCurrency": "选择货币"}, "validation": {"nameRequired": "公司名称是必需的", "emailRequired": "邮箱是必需的", "emailInvalid": "无效的邮箱地址", "addressRequired": "地址是必需的", "cityRequired": "城市是必需的", "stateRequired": "省/州是必需的", "zipRequired": "邮编是必需的"}, "create": {"title": "创建", "description": "创建新公司以管理业务关系。", "submit": "创建公司"}, "edit": {"title": "编辑公司", "submit": "更新公司"}, "details": {"actions": "操作"}, "actions": {"edit": "编辑公司", "editDetails": "编辑详情", "manageCustomerGroup": "管理客户群组", "approvalSettings": "审批设置", "delete": "删除公司", "confirmDelete": "确认删除"}, "deleteWarning": "这将永久删除公司及所有相关数据。", "delete": {"title": "删除公司", "description": "确定要删除此公司吗？此操作无法撤销。"}, "approvals": {"requiresAdminApproval": "需要管理员审批", "requiresSalesManagerApproval": "需要销售经理审批", "noApprovalRequired": "无需审批"}, "employees": {"title": "员工", "noEmployees": "此公司未找到员工", "name": "姓名", "email": "邮箱", "phone": "电话", "role": "角色", "spendingLimit": "消费限额", "admin": "管理员", "employee": "员工", "add": "添加", "create": {"title": "创建员工", "success": "员工创建成功", "error": "创建员工失败"}, "form": {"details": "详细信息", "permissions": "权限设置", "firstName": "名字", "lastName": "姓氏", "email": "邮箱", "phone": "电话", "spendingLimit": "消费限额", "adminAccess": "管理员权限", "adminAccessDescription": "授予此员工管理员权限", "isAdmin": "是管理员", "isAdminDesc": "授予此员工管理员权限", "isAdminTooltip": "管理员可以管理公司设置和其他员工", "firstNamePlaceholder": "请输入名字", "lastNamePlaceholder": "请输入姓氏", "emailPlaceholder": "请输入邮箱地址", "phonePlaceholder": "请输入电话号码", "spendingLimitPlaceholder": "请输入消费限额", "save": "保存", "saving": "保存中..."}, "toasts": {"updateSuccess": "员工信息更新成功", "updateError": "更新员工信息失败"}, "delete": {"confirmation": "确定要删除此员工吗？", "success": "员工删除成功"}, "edit": {"title": "编辑员工"}}, "toasts": {"createSuccess": "成功创建公司", "createError": "创建公司失败", "updateSuccess": "成功更新公司", "updateError": "更新公司失败", "deleteSuccess": "成功删除公司", "deleteError": "删除公司失败"}, "customerGroup": {"title": "管理客户群组", "name": "客户群组名称", "description": "为公司 \"{0}\" 管理客户群组。该公司有 {1} 名员工。", "noGroups": "暂无客户群组", "hint": "将此公司分配到客户群组以应用群组特定的定价和权限。", "groupName": "客户群组", "actions": "操作", "currentGroup": "当前群组", "selectGroup": "选择客户群组", "noGroup": "未分配群组", "add": "添加", "remove": "移除", "addSuccess": "成功将公司添加到客户群组", "addError": "添加公司到客户群组失败", "removeSuccess": "成功从客户群组中移除公司", "removeError": "从客户群组中移除公司失败", "updateSuccess": "客户群组更新成功", "updateError": "客户群组更新失败"}, "approvalSettings": {"title": "审批设置", "description": "配置公司的审批要求", "requiresAdminApproval": "需要管理员审批", "requiresAdminApprovalDesc": "来自此公司的订单在处理前需要管理员审批", "requiresSalesManagerApproval": "需要销售经理审批", "requiresSalesManagerApprovalDesc": "来自此公司的订单在处理前需要销售经理审批", "updateSuccess": "审批设置更新成功", "updateError": "审批设置更新失败"}}, "approvals": {"domain": "审批", "title": "审批", "subtitle": "管理审批工作流", "noApprovals": "未找到审批", "noApprovalsDescription": "当前没有需要审批的项目。", "table": {"id": "ID", "type": "类型", "company": "公司", "customer": "客户", "amount": "金额", "status": "状态", "createdAt": "创建时间"}, "status": {"pending": "待审批", "approved": "已批准", "rejected": "已拒绝", "expired": "已过期", "unknown": "未知状态"}, "details": {"header": "审批详情", "summary": "审批摘要", "company": "公司", "customer": "客户", "order": "订单", "amount": "金额", "updatedAt": "更新时间", "reason": "原因", "actions": "操作"}, "actions": {"approve": "批准", "reject": "拒绝", "confirmApprove": "确认批准", "confirmReject": "确认拒绝", "reasonPlaceholder": "输入原因（可选）..."}, "filters": {"status": "按状态筛选"}, "toasts": {"approveSuccess": "成功批准", "approveError": "批准失败", "rejectSuccess": "成功拒绝", "rejectError": "拒绝失败"}}}
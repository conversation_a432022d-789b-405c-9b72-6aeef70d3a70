import { But<PERSON>, <PERSON>er, Switch, Label, toast } from "@medusajs/ui"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { useUpdateApprovalSettings } from "../../../../../hooks/api/approvals"
import { Company } from "../../../../../types"

interface CompanyApprovalSettingsDrawerProps {
  company: Company
  open: boolean
  setOpen: (open: boolean) => void
}

export const CompanyApprovalSettingsDrawer = ({ 
  company, 
  open, 
  setOpen 
}: CompanyApprovalSettingsDrawerProps) => {
  const { t } = useTranslation()
  const [requiresAdminApproval, setRequiresAdminApproval] = useState(
    company.approval_settings?.requires_admin_approval || false
  )
  const [requiresSalesManagerApproval, setRequiresSalesManagerApproval] = useState(
    company.approval_settings?.requires_sales_manager_approval || false
  )

  const { mutateAsync, isPending } = useUpdateApprovalSettings(company.id, {
    onSuccess: () => {
      toast.success("审批设置更新成功")
      setOpen(false)
    },
    onError: () => {
      toast.error("更新审批设置失败")
    },
  })

  const handleSubmit = async () => {
    await mutateAsync({
      requires_admin_approval: requiresAdminApproval,
      requires_sales_manager_approval: requiresSalesManagerApproval,
    })
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <Drawer.Content className="z-50">
        <Drawer.Header>
          <Drawer.Title>审批设置</Drawer.Title>
        </Drawer.Header>
        
        <Drawer.Body className="p-4 space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label size="small">需要管理员审批</Label>
                <p className="text-ui-fg-muted text-sm">
                  启用后，该公司的订单需要管理员审批才能处理
                </p>
              </div>
              <Switch
                checked={requiresAdminApproval}
                onCheckedChange={setRequiresAdminApproval}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label size="small">需要销售经理审批</Label>
                <p className="text-ui-fg-muted text-sm">
                  启用后，该公司的订单需要销售经理审批才能处理
                </p>
              </div>
              <Switch
                checked={requiresSalesManagerApproval}
                onCheckedChange={setRequiresSalesManagerApproval}
              />
            </div>
          </div>
        </Drawer.Body>
        
        <Drawer.Footer>
          <Button variant="secondary" onClick={() => setOpen(false)}>
            取消
          </Button>
          <Button onClick={handleSubmit} isLoading={isPending}>
            保存
          </Button>
        </Drawer.Footer>
      </Drawer.Content>
    </Drawer>
  )
}
